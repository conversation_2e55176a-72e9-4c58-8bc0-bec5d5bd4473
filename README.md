# Plansquare-HRMS

## Project Overview

Plansquare-HRMS is a comprehensive Human Resource Management System built by PlanSquare Management Consultancy. This application provides a complete solution for managing organizational HR processes including employee management, attendance tracking, leave management, payroll, and performance evaluation.

## Getting Started

### Prerequisites

- Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- .NET Core 8.0 SDK
- SQL Server (for production deployment)

### Development Setup

Follow these steps to set up the development environment:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd hrms-plan

# Step 3: Install frontend dependencies
npm install

# Step 4: Start the development server
npm run dev
```

### Backend Setup

```sh
# Navigate to backend directory
cd backend

# Restore .NET packages
dotnet restore

# Run the backend API
dotnet run --project HRMS.API
```

## Technology Stack

### Frontend
- **React** - Modern UI library
- **TypeScript** - Type-safe JavaScript
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern component library
- **React Router** - Client-side routing

### Backend
- **.NET Core 8.0** - Cross-platform backend framework
- **Entity Framework Core** - ORM for database operations
- **SQL Server** - Production database
- **JWT Authentication** - Secure token-based authentication
- **Multi-tenant Architecture** - Organization-specific data isolation

## Features

- **Multi-tenant Architecture** - Support for multiple organizations
- **Role-based Access Control** - Super Admin, Organization Admin, and Employee roles
- **Employee Management** - Complete CRUD operations for employee data
- **Attendance Tracking** - Real-time check-in/check-out functionality
- **Leave Management** - Leave applications and approval workflow
- **Holiday Management** - Organizational and government holiday tracking
- **Performance Management** - Employee performance evaluation
- **Payroll Management** - Salary and compensation tracking
- **Recruitment Module** - Job postings and application management
- **Dashboard Analytics** - Comprehensive reporting and insights

## Deployment

The application supports single-port deployment where the .NET Core backend serves the React frontend. Use the provided Dockerfile for containerized deployment:

```sh
# Build and run with Docker
docker build -t plansquare-hrms .
docker run -p 5020:5020 plansquare-hrms
```

## License

© 2024 PlanSquare Management Consultancy. All rights reserved.
