{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "Urls": "http://localhost:5020;https://localhost:5021", "ConnectionStrings": {"DefaultConnection": "Server=**************,2829;Database=dbHRMS;User Id=userHRMS;Password=***********;TrustServerCertificate=true;Connection Timeout=30;"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "HRMS.API", "Audience": "HRMS.Client", "ExpirationMinutes": 1440}, "Database": {"SchemaPrefix": "org_", "SchemaSuffix": "", "AutoProvision": true, "TimeoutSeconds": 300, "ConnectionRetryCount": 3, "ConnectionRetryDelay": 5}, "AdminCredentials": {"GenerateOnStartup": true, "RequirePasswordReset": false, "SuperAdmin": {"Email": "<EMAIL>", "Name": "System Administrator", "Password": "Admin123!"}, "DefaultOrganization": {"Name": "Plan Square", "Domain": "plan2intl.com", "Industry": "Technology", "AdminEmail": "<EMAIL>", "AdminName": "<PERSON><PERSON><PERSON>"}, "Passwords": {"<EMAIL>": "Admin123!"}}, "Security": {"EnableMobileBlocking": true, "MobileBlockingMessage": "This HRMS application is designed for desktop/laptop computers only. Please access from a desktop or laptop computer.", "AllowedDeviceTypes": ["Desktop", "Laptop"], "BlockedDeviceTypes": ["Mobile", "Tablet", "Smartphone"]}}