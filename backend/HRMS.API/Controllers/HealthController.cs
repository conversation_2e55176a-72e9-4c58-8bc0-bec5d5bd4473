using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HRMS.Application.Common;
using HRMS.Core.Interfaces;

namespace HRMS.API.Controllers;

[ApiController]
[Route("api/v1/health")]
public class HealthController : BaseController
{
    private readonly ILogger<HealthController> _logger;
    private readonly IMasterDatabaseService _masterDatabaseService;

    public HealthController(ILogger<HealthController> logger, IMasterDatabaseService masterDatabaseService)
    {
        _logger = logger;
        _masterDatabaseService = masterDatabaseService;
    }

    /// <summary>
    /// Health Check
    /// </summary>
    /// <returns>API health status</returns>
    [HttpGet]
    [AllowAnonymous]
    public async Task<ActionResult<ApiResponse<object>>> HealthCheck()
    {
        try
        {
            // Test database connectivity using master database service
            var organizations = await _masterDatabaseService.GetOrganizationCountAsync();

            var healthData = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development",
                Database = new
                {
                    Status = "Connected",
                    OrganizationCount = organizations
                },
                Services = await GetServiceHealthStatusAsync()
            };

            return Ok(ApiResponse<object>.SuccessResult(healthData));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");

            var errorData = new
            {
                Status = "Unhealthy",
                Timestamp = DateTime.UtcNow,
                Error = ex.Message
            };

            return StatusCode(503, ApiResponse<object>.ErrorResult("HEALTH_CHECK_FAILED", "Health check failed", errorData));
        }
    }

    /// <summary>
    /// API Information
    /// </summary>
    /// <returns>API information and available endpoints</returns>
    [HttpGet("info")]
    [AllowAnonymous]
    public ActionResult<ApiResponse<object>> GetApiInfo()
    {
        var apiInfo = new
        {
            Name = "HRMS API",
            Version = "1.0.0",
            Description = "Human Resource Management System API",
            Documentation = "/swagger",
            Endpoints = new
            {
                Authentication = new[]
                {
                    "POST /api/v1/auth/login",
                    "POST /api/v1/auth/register-organization",
                    "POST /api/v1/auth/refresh",
                    "POST /api/v1/auth/logout"
                },
                UserManagement = new[]
                {
                    "GET /api/v1/users/me",
                    "PUT /api/v1/users/me",
                    "GET /api/v1/users",
                    "POST /api/v1/users",
                    "GET /api/v1/users/{id}",
                    "PUT /api/v1/users/{id}",
                    "DELETE /api/v1/users/{id}"
                },
                Attendance = new[]
                {
                    "POST /api/v1/attendance/checkin",
                    "POST /api/v1/attendance/checkout",
                    "GET /api/v1/attendance/records"
                },
                Leave = new[]
                {
                    "GET /api/v1/leave/types",
                    "GET /api/v1/leave/balance",
                    "POST /api/v1/leave/apply",
                    "GET /api/v1/leave/requests",
                    "PUT /api/v1/leave/requests/{id}/status"
                },
                Tasks = new[]
                {
                    "GET /api/v1/tasks",
                    "POST /api/v1/tasks",
                    "PUT /api/v1/tasks/{id}/progress",
                    "POST /api/v1/tasks/{id}/comments"
                },
                Performance = new[]
                {
                    "GET /api/v1/performance/reviews",
                    "POST /api/v1/performance/reviews"
                },
                Payroll = new[]
                {
                    "GET /api/v1/payroll/employee",
                    "GET /api/v1/payroll/benefits"
                },
                Recruitment = new[]
                {
                    "GET /api/v1/recruitment/jobs",
                    "POST /api/v1/recruitment/jobs",
                    "GET /api/v1/recruitment/jobs/{jobId}/applications"
                },
                SuperAdmin = new[]
                {
                    "GET /api/v1/super-admin/organizations",
                    "PUT /api/v1/super-admin/organizations/{id}/status",
                    "GET /api/v1/super-admin/analytics",
                    "GET /api/v1/super-admin/settings",
                    "PUT /api/v1/super-admin/settings"
                },
                OrganizationAdmin = new[]
                {
                    "GET /api/v1/org-admin/dashboard",
                    "GET /api/v1/org-admin/billing"
                }
            },
            Features = new[]
            {
                "JWT Authentication",
                "Role-based Authorization",
                "Multi-tenant Architecture",
                "Comprehensive Error Handling",
                "Input Validation",
                "Swagger Documentation",
                "Health Monitoring"
            }
        };

        return Ok(ApiResponse<object>.SuccessResult(apiInfo));
    }

    private async Task<object> GetServiceHealthStatusAsync()
    {
        try
        {
            // In a real system, this would check the actual health of each service
            // For now, we'll return a dynamic status based on basic checks
            var services = new
            {
                Authentication = "Available",
                UserManagement = "Available",
                AttendanceManagement = "Available",
                LeaveManagement = "Available",
                TaskManagement = "Available",
                PerformanceManagement = "Available",
                PayrollManagement = "Available",
                RecruitmentManagement = "Available",
                SuperAdminServices = "Available",
                OrganizationAdminServices = "Available"
            };

            return services;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking service health");
            return new
            {
                Authentication = "Unknown",
                UserManagement = "Unknown",
                AttendanceManagement = "Unknown",
                LeaveManagement = "Unknown",
                TaskManagement = "Unknown",
                PerformanceManagement = "Unknown",
                PayrollManagement = "Unknown",
                RecruitmentManagement = "Unknown",
                SuperAdminServices = "Unknown",
                OrganizationAdminServices = "Unknown"
            };
        }
    }
}
