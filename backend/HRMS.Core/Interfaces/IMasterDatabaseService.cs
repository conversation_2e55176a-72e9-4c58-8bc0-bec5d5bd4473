using HRMS.Core.Entities;

namespace HRMS.Core.Interfaces;

public interface IMasterDatabaseService
{
    System.Threading.Tasks.Task<bool> DomainExistsAsync(string domain);
    System.Threading.Tasks.Task<bool> EmailExistsAsync(string email);
    System.Threading.Tasks.Task<User?> GetUserByEmailAsync(string email);
    System.Threading.Tasks.Task<User?> GetUserByIdAsync(Guid userId);
    System.Threading.Tasks.Task<List<User>> GetUsersByOrganizationAsync(Guid organizationId);
    System.Threading.Tasks.Task<Organization?> GetOrganizationByIdAsync(Guid organizationId);
    System.Threading.Tasks.Task<List<Organization>> GetAllOrganizationsAsync();
    System.Threading.Tasks.Task<int> GetOrganizationCountAsync();
    System.Threading.Tasks.Task<List<User>> GetAllUsersAsync();
    System.Threading.Tasks.Task DeleteOrganizationAsync(Guid organizationId);
    System.Threading.Tasks.Task<Organization> CreateOrganizationAsync(Organization organization);
    System.Threading.Tasks.Task<User> CreateUserAsync(User user);
    System.Threading.Tasks.Task<User> UpdateUserAsync(User user);
    System.Threading.Tasks.Task DeleteUserAsync(Guid userId);
    System.Threading.Tasks.Task<Organization> UpdateOrganizationAsync(Organization organization);
    System.Threading.Tasks.Task<EmployeeDetail> CreateEmployeeDetailAsync(EmployeeDetail employeeDetail);
    System.Threading.Tasks.Task<EmployeeDetail> UpdateEmployeeDetailAsync(EmployeeDetail employeeDetail);
    System.Threading.Tasks.Task BeginTransactionAsync();
    System.Threading.Tasks.Task CommitTransactionAsync();
    System.Threading.Tasks.Task RollbackTransactionAsync();
}
