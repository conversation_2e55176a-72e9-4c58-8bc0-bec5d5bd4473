import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === 'development' &&
    componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    // Output to backend's wwwroot directory for single-port deployment
    outDir: './backend/HRMS.API/wwwroot',
    emptyOutDir: true,
    // Ensure assets are served from the correct path
    assetsDir: 'assets',
  },
  // Configure base path for production
  base: mode === 'production' ? '/' : '/',
}));
